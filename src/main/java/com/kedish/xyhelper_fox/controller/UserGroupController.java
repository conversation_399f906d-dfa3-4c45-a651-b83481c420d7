package com.kedish.xyhelper_fox.controller;

import com.kedish.xyhelper_fox.model.req.PageQueryReq;
import com.kedish.xyhelper_fox.model.resp.FoxPageResult;
import com.kedish.xyhelper_fox.model.resp.FoxResult;
import com.kedish.xyhelper_fox.service.UserGroupService;
import com.kedish.xyhelper_fox.model.resp.RateLimitVO;
import com.kedish.xyhelper_fox.repo.model.GroupRateLimit;
import com.kedish.xyhelper_fox.repo.model.UserGroup;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

import com.kedish.xyhelper_fox.model.req.AddUserGroupReq;
import com.kedish.xyhelper_fox.service.ChatgptUserService;
import com.kedish.xyhelper_fox.component.UserLimitBucketComponent;

@Slf4j
@RestController
@RequestMapping("/api/userGroup")
public class UserGroupController extends BaseController{

    @Resource
    private UserGroupService userGroupService;
    @Resource
    private ChatgptUserService chatgptUserService;
    @Resource
    private UserLimitBucketComponent userLimitBucketComponent;

    @Resource
    private ThreadPoolExecutor threadPoolExecutor;

    @PostMapping
    public FoxPageResult pageUserGroup(@RequestBody PageQueryReq req) {
        return FoxPageResult.fromPage(userGroupService.page(req));
    }

    @GetMapping("/detail")
    public FoxResult getGroupDetail(@RequestParam Long groupId) {
        UserGroup group = userGroupService.getById(groupId);
        if (group == null) {
            return FoxResult.fail("Group not found");
        }
        List<GroupRateLimit> groupRateLimits = userGroupService.getGroupRateLimit(groupId);
        List<RateLimitVO> rateLimitVOS = groupRateLimits.stream().map(limit -> {
            RateLimitVO vo = new RateLimitVO();
            vo.setModel(limit.getModel());
            vo.setLimit(limit.getRate() == null ? null : limit.getRate().longValue());
            vo.setPer(limit.getPeriod());
            vo.setMultiplier(limit.getMultiplier());
            vo.setSource("group"); // Indicate this is from group limits
            return vo;
        }).collect(Collectors.toList());
        return FoxResult.ok(java.util.Map.of("group", group, "rateLimits", rateLimitVOS));
    }


    @GetMapping("/detailByName")
    public FoxResult getGroupDetailByName(@RequestParam String groupName) {
        UserGroup group = userGroupService.getByName(groupName);
        if (group == null) {
            return FoxResult.fail("Group not found");
        }
        List<GroupRateLimit> groupRateLimits = userGroupService.getGroupRateLimit(group.getId());
        List<RateLimitVO> rateLimitVOS = groupRateLimits.stream().map(limit -> {
            RateLimitVO vo = new RateLimitVO();
            vo.setModel(limit.getModel());
            vo.setLimit(limit.getRate() == null ? null : limit.getRate().longValue());
            vo.setPer(limit.getPeriod());
            vo.setMultiplier(limit.getMultiplier());
            vo.setSource("group"); // Indicate this is from group limits
            return vo;
        }).collect(Collectors.toList());
        return FoxResult.ok(java.util.Map.of("group", group, "rateLimits", rateLimitVOS));
    }

    @GetMapping("/details")
    public FoxResult getGroupDetails(){

        List<UserGroup> allGroups = userGroupService.getAllGroups();
        Map<Long, UserGroup> map = allGroups
                .stream()
                .collect(Collectors.toMap(UserGroup::getId, e -> e));
        List<GroupRateLimit> allGroupRateLimit = userGroupService.getAllGroupRateLimit();

        List<RateLimitVO> rateLimitVOS = allGroupRateLimit.stream().map(limit -> {
            UserGroup userGroup = map.get(limit.getGroupId());
            if (userGroup == null) {
                return null;
            }
            RateLimitVO vo = new RateLimitVO();
            vo.setModel(limit.getModel());
            vo.setLimit(limit.getRate() == null ? null : limit.getRate().longValue());
            vo.setPer(limit.getPeriod());
            vo.setMultiplier(limit.getMultiplier());
            vo.setSource("group"); // Indicate this is from group limits
            vo.setGroupName(userGroup.getName());
            vo.setGroupTitle(userGroup.getTitle());
            return vo;
        }).filter(Objects::nonNull).toList();
        return FoxResult
                .ok(
                        rateLimitVOS
                                .stream()
                                .collect(Collectors.groupingBy(RateLimitVO::getGroupName))
                );

    }

    @PostMapping("/update")
    public FoxResult updateUserGroup(@RequestBody AddUserGroupReq req) {
        try {
            checkIsAdmin();
            userGroupService.updateUserGroupAndRateLimits(req);
            return FoxResult.ok();
        } catch (IllegalArgumentException e) {
            return FoxResult.fail(e.getMessage());
        }
    }

    @GetMapping("/resetGroupUserBucket")
    public FoxResult resetUserBucket(@RequestParam Long groupId) {
        log.info("resetGroupUserBucket: {}", groupId);
        checkIsAdmin();
        threadPoolExecutor.submit(()->{
            java.util.List<com.kedish.xyhelper_fox.repo.model.ChatgptUser> users = chatgptUserService.getUsersByGroupId(groupId);
            List<GroupRateLimit> groupRateLimit = userGroupService.getGroupRateLimit(groupId);
            for (com.kedish.xyhelper_fox.repo.model.ChatgptUser user : users) {
                userLimitBucketComponent.resetBucket(user, groupRateLimit);
            }

        });
        return FoxResult.ok();
    }
}
