package com.kedish.xyhelper_fox.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.kedish.xyhelper_fox.cache.LocalCache;
import com.kedish.xyhelper_fox.model.req.PageQueryReq;
import com.kedish.xyhelper_fox.repo.mapper.ChatGptUserMapper;
import com.kedish.xyhelper_fox.repo.mapper.GroupRateLimitMapper;
import com.kedish.xyhelper_fox.repo.mapper.UserGroupMapper;
import com.kedish.xyhelper_fox.repo.mapper.UserRateLimitMapper;
import com.kedish.xyhelper_fox.repo.model.ChatgptUser;
import com.kedish.xyhelper_fox.repo.model.GroupRateLimit;
import com.kedish.xyhelper_fox.repo.model.UserGroup;
import com.kedish.xyhelper_fox.repo.model.UserRateLimit;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.util.StringUtils;
import com.kedish.xyhelper_fox.model.req.AddUserGroupReq;
import com.kedish.xyhelper_fox.model.req.AddUserGroupReq.RateLimitItem;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class UserGroupService {

    @Resource
    private UserGroupMapper userGroupMapper;

    @Resource
    private GroupRateLimitMapper groupRateLimitMapper;

    @Resource
    private UserRateLimitMapper userRateLimitMapper;

    @Resource
    private LocalCache localCache;

    public UserGroup getByName(String name) {
        return userGroupMapper.selectOne(
                new QueryWrapper<UserGroup>()
                        .eq("name", name)
        );
    }
    public List<GroupRateLimit> getGroupRateLimit(Long groupId) {

        return groupRateLimitMapper.selectList(
                new QueryWrapper<GroupRateLimit>()
                        .eq("group_id", groupId)
        );
    }

    /**
     * Get user-specific rate limits for a given user token
     *
     * @param userToken The user token to get rate limits for
     * @return List of user rate limits
     */
    public List<UserRateLimit> getUserRateLimit(String userToken) {
        return userRateLimitMapper.selectList(
                new QueryWrapper<UserRateLimit>()
                        .eq("user_token", userToken)
        );
    }

    public Page<UserGroup> page(PageQueryReq req){
        Page<UserGroup> page = new Page<>(req.getPageNum(), req.getPageSize());
        if (StringUtils.hasLength(req.getSortField()) && StringUtils.hasLength(req.getSortOrder())) {
            OrderItem orderItem = new OrderItem();
            orderItem.setColumn(req.getSortField());
            orderItem.setAsc("asc".equalsIgnoreCase(req.getSortOrder()));
            page.addOrder(orderItem);
        }
        QueryWrapper<UserGroup> queryWrapper = new QueryWrapper<>();
        if (StringUtils.hasLength(req.getQuery())) {
            queryWrapper.and(wrapper -> wrapper.like("name", req.getQuery())
                    .or().like("description", req.getQuery()));
        }
        return userGroupMapper.selectPage(page, queryWrapper);
    }

    public UserGroup getById(Long id) {
        return userGroupMapper.selectById(id);
    }

    /**
     * Update or create user-specific rate limits
     *
     * @param userToken The user token to update rate limits for
     * @param rateLimits List of rate limit items to apply
     */
    @Transactional
    public void updateUserRateLimits(String userToken, List<AddUserGroupReq.RateLimitItem> rateLimits) {
        // 1. Delete existing rate limits for this user
        userRateLimitMapper.delete(new QueryWrapper<UserRateLimit>().eq("user_token", userToken));

        // 2. Insert new rate limits
        if (rateLimits != null && !rateLimits.isEmpty()) {
            for (AddUserGroupReq.RateLimitItem item : rateLimits) {
                UserRateLimit limit = new UserRateLimit();
                limit.setUserToken(userToken);
                limit.setModel(item.getModel());
                limit.setRate(item.getRate());
                limit.setPeriod(item.getPeriod());
                limit.setMultiplier(item.getMultiplier());
                userRateLimitMapper.insert(limit);
            }
        }
        // 不再清除缓存，由resetBucket负责
    }


    @Transactional
    public void updateUserGroupAndRateLimits(AddUserGroupReq req) {
        // 校验是否为受保护的默认分组
        if (req.getId() != null && isProtectedGroup(req.getId())) {
            throw new IllegalArgumentException("默认分组(-1~3)不允许修改");
        }

        // 校验name是否重名
        UserGroup exist = userGroupMapper.selectOne(new QueryWrapper<UserGroup>().eq("name", req.getName()));
        if (exist != null && (req.getId() == null || !exist.getId().equals(req.getId()))) {
            throw new IllegalArgumentException("分组名称已存在");
        }

        // 1. 更新或插入UserGroup
        UserGroup group = new UserGroup();
        group.setId(req.getId());
        group.setName(req.getName());
        group.setTitle(req.getTitle());
        group.setDescription(req.getDescription());
        group.setIsDeleted(req.getIsDeleted());
        group.setAvailableNodes(req.getAvailableNodes());
        group.setAutoMiniShareGpt4Limit(req.getAutoMiniShareGpt4Limit());

        if (group.getId() == null) {
            // 新增分组
            userGroupMapper.insert(group);
        } else {
            // 更新分组
            userGroupMapper.updateById(group);
        }

        Long groupId = group.getId();
        if (groupId == null) {
            groupId = userGroupMapper.selectOne(new QueryWrapper<UserGroup>().eq("name", group.getName())).getId();
        }

        // 2. 删除原有限速信息
        groupRateLimitMapper.delete(new QueryWrapper<GroupRateLimit>().eq("group_id", groupId));

        // 3. 批量插入新限速信息
        if (req.getRateLimits() != null && !req.getRateLimits().isEmpty()) {
            for (RateLimitItem item : req.getRateLimits()) {
                GroupRateLimit limit = new GroupRateLimit();
                limit.setGroupId(groupId);
                limit.setModel(item.getModel());
                limit.setRate(item.getRate());
                limit.setPeriod(item.getPeriod());
                limit.setMultiplier(item.getMultiplier());
                groupRateLimitMapper.insert(limit);
            }
        }
    }

    /**
     * 检查是否为受保护的默认分组
     * @param groupId 分组ID
     * @return true if protected
     */
    private boolean isProtectedGroup(Long groupId) {
        return groupId != null && groupId >= -1 && groupId <= 3;
    }

    /**
     * 删除用户分组
     * @param groupId 分组ID
     */
    @Transactional
    public void deleteUserGroup(Long groupId) {
        if (isProtectedGroup(groupId)) {
            throw new IllegalArgumentException("默认分组(-1~3)不允许删除");
        }

        UserGroup group = userGroupMapper.selectById(groupId);
        if (group == null) {
            throw new IllegalArgumentException("分组不存在");
        }

        // 删除分组的限速配置
        groupRateLimitMapper.delete(new QueryWrapper<GroupRateLimit>().eq("group_id", groupId));

        // 删除分组
        userGroupMapper.deleteById(groupId);
    }


    /**
     * 获取所有用户组
     * @return 所有用户组列表
     */
    public List<UserGroup> getAllGroups() {
        return userGroupMapper.selectList(null);
    }

    public List<GroupRateLimit> getAllGroupRateLimit() {
        return groupRateLimitMapper.selectList(null);
    }
}
