<template>
    <div class="pagination-container" style="margin-bottom: 10px;">
        <!-- 搜索栏 -->
        <el-row :gutter="20" class="search-bar">
            <el-col :span="isMobile ? 16 : 4">
                <el-input v-model="searchQuery.query" placeholder="分组名称/描述"></el-input>
            </el-col>
            <el-col :span="isMobile ? 8 : 2">
                <el-button type="primary" @click="fetchData" :icon="Search">查询</el-button>
            </el-col>
        </el-row>

        <!-- 桌面端表格 -->
        <div v-if="!isMobile" class="desktop-table">
            <el-table :data="tableData" border @sort-change="sortCustomer" style="margin-top: 10px;" max-height="500px"
                :header-cell-style="{ 'text-align': 'center' }" :cell-style="{ 'text-align': 'center' }"
                @selection-change="handleSelectionChange">
                <el-table-column type="selection" />
                <el-table-column prop="id" label="ID" />
                <el-table-column prop="title" label="名称" />
                <el-table-column prop="name" label="标识" />
                <el-table-column prop="description" label="描述" />
                <el-table-column prop="availableNodes" label="可用节点" />
                <el-table-column prop="autoMiniShareGpt4Limit" label="共享4o速率限制">
                    <template #default="scope">
                        {{ scope.row.autoMiniShareGpt4Limit ? '是' : '否' }}
                    </template>
                </el-table-column>
                <el-table-column prop="createdAt" label="创建时间" sortable="custom" :formatter="formatDate" />
                <el-table-column prop="updatedAt" label="修改时间" sortable="custom" :formatter="formatDate" />
                <el-table-column label="操作">
                    <template #default="scope">
                        <el-button size="small" type="primary" @click="editGroup(scope.row)">编辑</el-button>
                        <el-button size="small" type="warning" @click="resetGroupRateLimit(scope.row)">重置速率</el-button>
                    </template>
                </el-table-column>
            </el-table>
        </div>

        <!-- 移动端卡片列表 -->
        <div v-else class="mobile-card-list">
            <el-card v-for="item in tableData" :key="item.id" class="mobile-card" shadow="hover">
                <div class="mobile-card-header">
                    <span class="mobile-card-title">{{ item.title }}</span>
                    <el-tag size="small" :type="item.autoMiniShareGpt4Limit ? 'success' : 'info'">
                        {{ item.autoMiniShareGpt4Limit ? '共享4o' : '独立限制' }}
                    </el-tag>
                </div>
                <div class="mobile-card-content">
                    <div class="mobile-card-item">
                        <span class="label">ID:</span>
                        <span class="value">{{ item.id }}</span>
                    </div>
                    <div class="mobile-card-item">
                        <span class="label">标识:</span>
                        <span class="value">{{ item.name }}</span>
                    </div>
                    <div class="mobile-card-item">
                        <span class="label">描述:</span>
                        <span class="value">{{ item.description || '-' }}</span>
                    </div>
                    <div class="mobile-card-item">
                        <span class="label">可用节点:</span>
                        <span class="value">{{ item.availableNodes || '-' }}</span>
                    </div>
                    <div class="mobile-card-item">
                        <span class="label">创建时间:</span>
                        <span class="value">{{ formatDate(item, null, item.createdAt) }}</span>
                    </div>
                </div>
                <div class="mobile-card-footer">
                    <el-button size="small" type="primary" @click="editGroup(item)">编辑</el-button>
                    <el-button size="small" type="warning" @click="resetGroupRateLimit(item)">重置速率</el-button>
                </div>
            </el-card>
        </div>

        <el-pagination v-model:currentPage="searchQuery.pageNum" :page-size="searchQuery.pageSize"
            :total="searchQuery.totalItems" :layout="isMobile ? 'prev, pager, next' : 'total, prev, pager, next'"
            :page-sizes="[10, 20, 50, 100]" @current-change="handlePageChange" background class="pagination" />
    </div>

    <!-- 编辑对话框 -->
    <el-dialog title="添加/编辑分组" v-model="dialogVisible" :width="isMobile ? '95%' : '900px'" :fullscreen="isMobile">
        <el-form :model="form" :rules="rules" ref="formRef" :label-width="isMobile ? '90px' : '120px'">
            <el-form-item label="名称" prop="title">
                <el-input v-model="form.title" placeholder="请输入标题"></el-input>
            </el-form-item>
            <el-form-item label="标识" prop="name">
                <el-input v-model="form.name" placeholder="请输入分组名称"></el-input>
            </el-form-item>
            <el-form-item label="描述" prop="description">
                <el-input v-model="form.description" placeholder="请输入描述"></el-input>
            </el-form-item>
            <el-form-item label="可用节点" prop="availableNodes">
                <el-select v-model="availableNodesArr" multiple placeholder="请选择可用节点" style="width: 100%">
                    <el-option label="free" value="free" />
                    <el-option label="4o" value="4o" />
                    <el-option label="plus" value="plus" />
                    <el-option label="claude" value="claude" />
                    <el-option label="grok" value="grok" />
                </el-select>
            </el-form-item>
            <el-form-item label="共享4o速率限制" prop="autoMiniShareGpt4Limit">
                <el-switch v-model="form.autoMiniShareGpt4Limit"></el-switch>
                <div style="color: #909399; font-size: 12px; margin-top: 5px;">开启后，auto和mini模型将共享4o速率限制</div>
            </el-form-item>
            <el-divider>模型限速配置</el-divider>
            <el-row class="rate-limit-header" style="font-weight:bold; margin-bottom: 8px; text-align:center;">
                <el-col :span="4">模型</el-col>
                <el-col :span="6">周期</el-col>
                <el-col :span="6">速率</el-col>
                <el-col :span="6">倍数</el-col>
                <el-col :span="2"></el-col>
            </el-row>
            <el-row v-for="(item, idx) in filteredRateLimits" :key="item._key" :gutter="10" style="margin-bottom: 10px; align-items: center;">
                <el-col :span="4">
                    <el-input v-if="item.isCustom" v-model="item.model" placeholder="模型名" />
                    <el-text v-else>{{ item.label }}</el-text>
                </el-col>
                <el-col :span="6">
                    <el-select v-model="item.period" placeholder="选择周期">
                        <el-option v-for="per in useLimitPers" :key="per.value" :label="per.label" :value="per.value" />
                    </el-select>
                </el-col>
                <el-col :span="6">
                    <el-input v-model.number="item.rate" placeholder="速率" type="number" min="0" />
                </el-col>
                <el-col :span="6">
                    <el-input v-model.number="item.multiplier" placeholder="倍数(可选)" type="number" min="1" />
                </el-col>
                <el-col :span="2" style="text-align:center;">
                    <el-button v-if="item.isCustom" type="danger" size="small" :icon="Delete" @click="removeCustomModel(item)"></el-button>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="24" style="text-align:left;">
                    <el-button type="primary" plain icon="el-icon-plus" @click="addCustomModel">添加自定义模型</el-button>
                </el-col>
            </el-row>
        </el-form>
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="dialogVisible = false">取消</el-button>
                <el-button type="primary" @click="submitForm">确定</el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script setup>
import { ref, reactive, getCurrentInstance, computed, onMounted, onUnmounted } from 'vue';
import api from '@/axios';
import { ElMessage } from 'element-plus';
import { Search, Delete } from '@element-plus/icons-vue';

const { proxy } = getCurrentInstance();
const dialogVisible = ref(false);
const tableData = ref([]);
const selected = ref([]);
const form = reactive({
    id: null,
    title: '',
    name: '',
    description: '',
    availableNodes: '',
    autoMiniShareGpt4Limit: false
});
const availableNodesArr = ref([]);
const defaultForm = {
    id: null,
    title: '',
    name: '',
    description: '',
    availableNodes: '',
    autoMiniShareGpt4Limit: false
};
const searchQuery = ref({
    query: '',
    pageNum: 1,
    pageSize: 10,
    sortField: '',
    sortOrder: '',
    totalItems: 0
});
const rules = ref({
    title: [{ required: true, message: '请输入标题', trigger: 'blur' }],
    name: [{ required: true, message: '请输入分组名称', trigger: 'blur' }]
});
const rateLimitModels = [
    { model: 'auto', label: 'auto' },
    { model: 'gpt-4o-mini', label: 'gpt-4o-mini' },
    { model: 'gpt-4o', label: 'gpt-4o' },
    { model: 'o4-mini-high', label: 'o4-mini-high' },
    { model: 'o4-mini', label: 'o4-mini' },
    { model: 'o3', label: 'o3' },
    { model: 'gpt-4-5', label: 'gpt-4-5' },
    {model: 'gpt-5', label: 'gpt-5'},
    {model: 'gpt-5-pro', label: 'gpt-5-pro'},
    {model: 'gpt-5-thinking', label: 'gpt-5-thinking'}
];
const useLimitPers = [
    { value: '1h', label: '1小时' },
    { value: '3h', label: '3小时' },
    { value: '1d', label: '每天' },
    { value: '1w', label: '每周' }
];
let uniqueKey = 0;
const getDefaultRateLimits = () => rateLimitModels.map(m => ({
    model: m.model,
    label: m.label,
    period: '1h',
    rate: 0,
    multiplier: 1,
    isCustom: false,
    _key: uniqueKey++
}));
const rateLimits = ref(getDefaultRateLimits());
const addCustomModel = () => {
    rateLimits.value.push({
        model: '',
        label: '',
        period: '1h',
        rate: 0,
        multiplier: 1,
        isCustom: true,
        _key: uniqueKey++
    });
};
const removeCustomModel = (modelToRemove) => {
    const index = rateLimits.value.findIndex(item => item._key === modelToRemove._key);
    if (index !== -1) {
        rateLimits.value.splice(index, 1);
    }
};
const handleSelectionChange = (val) => {
    selected.value = val.map(item => item.id);
};
const openAddDialog = () => {
    Object.assign(form, defaultForm);
    rateLimits.value = getDefaultRateLimits();
    availableNodesArr.value = [];
    dialogVisible.value = true;
};
const editGroup = async (row) => {
    const res = await api.get('/api/userGroup/detail', { params: { groupId: row.id } });
    const group = res.data.data.group;
    Object.assign(form, group);
    // 分割可用节点
    availableNodesArr.value = group.availableNodes ? group.availableNodes.split(',') : [];
    const limits = res.data.data.rateLimits || [];
    // 固定模型
    const fixed = rateLimitModels.map(m => {
        const found = limits.find(l => l.model === m.model);
        return {
            model: m.model,
            label: m.label,
            period: found?.per || '1h',
            rate: found?.limit || 0,
            multiplier: found?.multiplier || 1,
            isCustom: false,
            _key: uniqueKey++
        };
    });
    // 自定义模型
    const customs = limits.filter(l => !rateLimitModels.some(m => m.model === l.model)).map(l => ({
        model: l.model,
        label: '',
        period: l.per || '1h',
        rate: l.limit || 0,
        multiplier: l.multiplier || 1,
        isCustom: true,
        _key: uniqueKey++
    }));
    rateLimits.value = [...fixed, ...customs];
    dialogVisible.value = true;
};
const handlePageChange = (page) => {
    searchQuery.value.pageNum = page;
    fetchData();
};
const fetchData = async () => {
    const res = await api.post('/api/userGroup', searchQuery.value);
    tableData.value = res.data.data;
    searchQuery.value.totalItems = res.data.total;
};
fetchData();
const formatDate = (row, col, cellvalue) => {
    return proxy.$dateFormat(cellvalue);
};
const submitForm = async () => {
    await proxy.$refs.formRef.validate();
    const formCopy = { ...form };
    formCopy.availableNodes = availableNodesArr.value.join(',');
    formCopy.rateLimits = rateLimits.value.map(l => ({
        model: l.model,
        period: l.period,
        rate: l.rate,
        multiplier: l.multiplier,
        per: l.period,
        limit: l.rate
    })).filter(l => l.model); // 过滤掉未填写模型名的自定义项
    const res = await api.post('/api/userGroup/update', formCopy);
    if (res.data.code === 0) {
        ElMessage.success('操作成功');
        dialogVisible.value = false;
        fetchData();
        Object.assign(form, defaultForm);
    } else {
        ElMessage.error(res.data.msg);
    }
};
const sortCustomer = ({ prop, order }) => {
    const sortOrder = order === 'ascending' ? 'asc' : order === 'descending' ? 'desc' : '';
    searchQuery.value.sortField = prop;
    searchQuery.value.sortOrder = sortOrder;
    fetchData();
};

// 新增：根据共享4o速率限制动态过滤rateLimits
const filteredRateLimits = computed(() => {
    if (form.autoMiniShareGpt4Limit) {
        return rateLimits.value.filter(item => !(item.model === 'auto' || item.model === 'gpt-4o-mini'));
    }
    return rateLimits.value;
});

const resetGroupRateLimit = async (row) => {
    try {
        await api.get('/api/userGroup/resetGroupUserBucket', { params: { groupId: row.id } });
        ElMessage.success('重置分组用户速率任务已提交');
    } catch (e) {
        ElMessage.error('重置失败');
    }
};

// 添加移动端检测
const isMobile = ref(false);
const checkMobile = () => {
    isMobile.value = window.innerWidth <= 768;
};

onMounted(() => {
    checkMobile();
    window.addEventListener('resize', checkMobile);
});

onUnmounted(() => {
    window.removeEventListener('resize', checkMobile);
});
</script>

<style scoped>
.pagination-container {
    padding: 20px;
}

.search-bar {
    margin-bottom: 15px;
}

/* 桌面端表格样式 */
.desktop-table {
    margin-bottom: 20px;
}

.el-table {
    margin-bottom: 20px;
    text-align: center;
}

/* 移动端卡片列表样式 */
.mobile-card-list {
    margin-top: 15px;
}

.mobile-card {
    margin-bottom: 15px;
    border-radius: 8px;
}

.mobile-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.mobile-card-title {
    font-size: 16px;
    font-weight: bold;
}

.mobile-card-content {
    margin-bottom: 15px;
}

.mobile-card-item {
    display: flex;
    margin-bottom: 8px;
    font-size: 14px;
}

.mobile-card-item .label {
    color: #909399;
    min-width: 80px;
}

.mobile-card-item .value {
    color: #303133;
    flex: 1;
    word-break: break-all;
}

.mobile-card-footer {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.mobile-card-footer .el-button {
    flex: 1;
    min-width: 80px;
}

/* 响应式样式调整 */
@media screen and (max-width: 768px) {
    .pagination-container {
        padding: 10px;
    }

    .search-bar {
        margin-bottom: 10px;
    }

    .el-pagination {
        justify-content: center;
        margin-top: 15px;
    }

    .dialog-footer {
        display: flex;
        justify-content: flex-end;
        gap: 8px;
    }

    .dialog-footer .el-button {
        flex: 1;
    }

    /* 移动端表单样式调整 */
    :deep(.el-form-item) {
        margin-bottom: 15px;
    }

    :deep(.el-form-item__label) {
        font-size: 14px;
    }

    :deep(.el-input),
    :deep(.el-select) {
        font-size: 14px;
    }

    :deep(.el-dialog__body) {
        padding: 15px !important;
    }

    :deep(.el-dialog__header) {
        padding: 15px !important;
    }

    :deep(.el-dialog__footer) {
        padding: 10px 15px !important;
    }
}

/* 动画效果 */
.fade-enter-active,
.fade-leave-active {
    transition: opacity 0.5s;
}

.fade-enter,
.fade-leave-to {
    opacity: 0;
}
</style> 